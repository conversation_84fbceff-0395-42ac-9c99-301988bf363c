# TRMT10C相关能量代谢基因相关性热图分析
# 作者: [您的姓名]
# 日期: 2025-07-08
# 数据来源: 
# - CPTAC (Cell 2021): mRNA FPKM vs TRMT10C蛋白丰度 (80样本)
# - CPTAC (Cell 2021): 蛋白丰度 vs TRMT10C蛋白丰度 (80样本)  
# - TCGA PanCancer Atlas: 蛋白表达RPPA vs TRMT10C mRNA (487样本)
# - TCGA Firehose Legacy: 蛋白表达RPPA vs TRMT10C mRNA (511样本)

# 加载必需的包
library(pheatmap)
library(RColorBrewer)
library(dplyr)
library(readr)
library(tidyr)
library(ggplot2)

# 设置工作目录
setwd(".")

# 1. 数据读取和预处理
cat("=== 数据读取和预处理 ===\n")

# 读取能量代谢相关基因列表
energy_genes_raw <- read_lines("能量代谢相关基因.txt")
energy_genes <- unlist(strsplit(energy_genes_raw, ", "))
# 移除控制基因和空值
energy_genes <- energy_genes[!energy_genes %in% c("ACTB", "GAPDH", "HPRT1", "18S", "NTC", "")]
cat("能量代谢相关基因数量:", length(energy_genes), "\n")

# 读取CPTAC mRNA数据
cptac_mrna <- read_tsv("CPTAC_Cell 2021_80 Samples/mRNAFPKM vs TRMT10C蛋白丰度 (CPTAC, Cell 2021).tsv")
cat("CPTAC mRNA数据维度:", nrow(cptac_mrna), "x", ncol(cptac_mrna), "\n")

# 读取CPTAC蛋白数据
cptac_protein <- read_tsv("CPTAC_Cell 2021_80 Samples/蛋白丰度vs TRMT10C蛋白丰度 (CPTAC, Cell 2021).tsv")
cat("CPTAC蛋白数据维度:", nrow(cptac_protein), "x", ncol(cptac_protein), "\n")

# 读取TCGA PanCancer数据
tcga_pancancer <- read_tsv("TCGA_PanCancer Atlas 487 Samples/蛋白表达RPPA vs TRMT10CmRNA (TCGA, PanCancer Atlas).tsv")
cat("TCGA PanCancer数据维度:", nrow(tcga_pancancer), "x", ncol(tcga_pancancer), "\n")

# 读取TCGA Firehose数据
tcga_firehose <- read_tsv("TCGA_Firehose Legacy_511 Samples/蛋白表达RPPA vs TRMT10CmRNA (TCGA, Firehose Legacy).tsv")
cat("TCGA Firehose数据维度:", nrow(tcga_firehose), "x", ncol(tcga_firehose), "\n")

# 2. 数据筛选和整合
cat("\n=== 数据筛选和整合 ===\n")

# 筛选能量代谢相关基因
filter_energy_genes <- function(data, gene_list) {
  filtered <- data[data$`Correlated Gene` %in% gene_list, ]
  cat("筛选后基因数量:", nrow(filtered), "\n")
  return(filtered)
}

cptac_mrna_filtered <- filter_energy_genes(cptac_mrna, energy_genes)
cptac_protein_filtered <- filter_energy_genes(cptac_protein, energy_genes)
tcga_pancancer_filtered <- filter_energy_genes(tcga_pancancer, energy_genes)
tcga_firehose_filtered <- filter_energy_genes(tcga_firehose, energy_genes)

# 创建相关性矩阵
create_correlation_matrix <- function() {
  # 获取所有数据集中的能量代谢基因
  all_genes <- unique(c(
    cptac_mrna_filtered$`Correlated Gene`,
    cptac_protein_filtered$`Correlated Gene`,
    tcga_pancancer_filtered$`Correlated Gene`,
    tcga_firehose_filtered$`Correlated Gene`
  ))
  
  # 创建空矩阵
  correlation_matrix <- matrix(NA, nrow = length(all_genes), ncol = 4)
  rownames(correlation_matrix) <- all_genes
  colnames(correlation_matrix) <- c(
    "CPTAC_mRNA_vs_Protein",
    "CPTAC_Protein_vs_Protein", 
    "TCGA_PanCancer_RPPA_vs_mRNA",
    "TCGA_Firehose_RPPA_vs_mRNA"
  )
  
  # 填充相关性数据
  for (gene in all_genes) {
    # CPTAC mRNA vs TRMT10C蛋白
    idx <- which(cptac_mrna_filtered$`Correlated Gene` == gene)
    if (length(idx) > 0) {
      correlation_matrix[gene, "CPTAC_mRNA_vs_Protein"] <- cptac_mrna_filtered$`Spearman's Correlation`[idx[1]]
    }
    
    # CPTAC 蛋白 vs TRMT10C蛋白
    idx <- which(cptac_protein_filtered$`Correlated Gene` == gene)
    if (length(idx) > 0) {
      correlation_matrix[gene, "CPTAC_Protein_vs_Protein"] <- cptac_protein_filtered$`Spearman's Correlation`[idx[1]]
    }
    
    # TCGA PanCancer RPPA vs TRMT10C mRNA
    idx <- which(tcga_pancancer_filtered$`Correlated Gene` == gene)
    if (length(idx) > 0) {
      correlation_matrix[gene, "TCGA_PanCancer_RPPA_vs_mRNA"] <- tcga_pancancer_filtered$`Spearman's Correlation`[idx[1]]
    }
    
    # TCGA Firehose RPPA vs TRMT10C mRNA
    idx <- which(tcga_firehose_filtered$`Correlated Gene` == gene)
    if (length(idx) > 0) {
      correlation_matrix[gene, "TCGA_Firehose_RPPA_vs_mRNA"] <- tcga_firehose_filtered$`Spearman's Correlation`[idx[1]]
    }
  }
  
  return(correlation_matrix)
}

correlation_matrix <- create_correlation_matrix()
cat("相关性矩阵维度:", nrow(correlation_matrix), "x", ncol(correlation_matrix), "\n")
cat("有效数据点数量:", sum(!is.na(correlation_matrix)), "\n")

# 3. 数据质量检查
cat("\n=== 数据质量检查 ===\n")
cat("各数据集中的基因数量:\n")
cat("- CPTAC mRNA:", nrow(cptac_mrna_filtered), "\n")
cat("- CPTAC Protein:", nrow(cptac_protein_filtered), "\n") 
cat("- TCGA PanCancer:", nrow(tcga_pancancer_filtered), "\n")
cat("- TCGA Firehose:", nrow(tcga_firehose_filtered), "\n")

# 检查相关性分布
cat("\n相关性分布统计:\n")
for (i in 1:ncol(correlation_matrix)) {
  col_data <- correlation_matrix[, i]
  valid_data <- col_data[!is.na(col_data)]
  if (length(valid_data) > 0) {
    cat(sprintf("%s: 均值=%.3f, 中位数=%.3f, 范围=[%.3f, %.3f], N=%d\n",
                colnames(correlation_matrix)[i],
                mean(valid_data), median(valid_data),
                min(valid_data), max(valid_data), length(valid_data)))
  }
}

# 4. 热图绘制
cat("\n=== 热图绘制 ===\n")

# 只保留至少有一个数据点的基因
genes_with_data <- rownames(correlation_matrix)[rowSums(!is.na(correlation_matrix)) > 0]
heatmap_matrix <- correlation_matrix[genes_with_data, ]

# 只保留有数据的列
cols_with_data <- colnames(heatmap_matrix)[colSums(!is.na(heatmap_matrix)) > 0]
heatmap_matrix <- heatmap_matrix[, cols_with_data]

cat("热图包含基因数量:", length(genes_with_data), "\n")
cat("热图包含数据集数量:", length(cols_with_data), "\n")

# 创建注释信息
create_annotations <- function() {
  # 列注释 - 数据集信息
  dataset_info <- data.frame(
    Dataset = character(0),
    Comparison = character(0),
    Sample_Size = character(0),
    stringsAsFactors = FALSE
  )

  for (col in cols_with_data) {
    if (col == "CPTAC_mRNA_vs_Protein") {
      dataset_info <- rbind(dataset_info, data.frame(
        Dataset = "CPTAC",
        Comparison = "mRNA_vs_Protein",
        Sample_Size = "80",
        stringsAsFactors = FALSE
      ))
    } else if (col == "CPTAC_Protein_vs_Protein") {
      dataset_info <- rbind(dataset_info, data.frame(
        Dataset = "CPTAC",
        Comparison = "Protein_vs_Protein",
        Sample_Size = "80",
        stringsAsFactors = FALSE
      ))
    } else if (col == "TCGA_PanCancer_RPPA_vs_mRNA") {
      dataset_info <- rbind(dataset_info, data.frame(
        Dataset = "TCGA",
        Comparison = "RPPA_vs_mRNA",
        Sample_Size = "487",
        stringsAsFactors = FALSE
      ))
    } else if (col == "TCGA_Firehose_RPPA_vs_mRNA") {
      dataset_info <- rbind(dataset_info, data.frame(
        Dataset = "TCGA",
        Comparison = "RPPA_vs_mRNA",
        Sample_Size = "511",
        stringsAsFactors = FALSE
      ))
    }
  }

  rownames(dataset_info) <- cols_with_data
  return(dataset_info)
}

col_annotation <- create_annotations()

# 设置颜色
annotation_colors <- list(
  Dataset = c("CPTAC" = "#E31A1C", "TCGA" = "#1F78B4"),
  Comparison = c("mRNA_vs_Protein" = "#A6CEE3",
                 "Protein_vs_Protein" = "#B2DF8A",
                 "RPPA_vs_mRNA" = "#FDBF6F"),
  Sample_Size = c("80" = "#CAB2D6", "487" = "#FFFF99", "511" = "#FB9A99")
)

# 绘制主热图
png("TRMT10C_energy_metabolism_heatmap.png", width = 10, height = 12, units = "in", res = 300)

pheatmap(
  heatmap_matrix,
  color = colorRampPalette(c("#2166AC", "white", "#B2182B"))(100),
  breaks = seq(-1, 1, length.out = 101),
  cluster_rows = TRUE,
  cluster_cols = FALSE,
  annotation_col = col_annotation,
  annotation_colors = annotation_colors,
  show_rownames = TRUE,
  show_colnames = TRUE,
  fontsize = 10,
  fontsize_row = 9,
  fontsize_col = 10,
  angle_col = 45,
  na_col = "grey90",
  main = "TRMT10C与能量代谢相关基因的Spearman相关性热图\n(CPTAC Cell 2021数据集)",
  cellwidth = 80,
  cellheight = 15
)

dev.off()

# 创建一个更详细的热图，按功能分组
create_functional_heatmap <- function() {
  # 按功能对基因进行分组
  complex_I <- grep("^NDUF", rownames(heatmap_matrix), value = TRUE)
  complex_II <- grep("^SDH", rownames(heatmap_matrix), value = TRUE)
  complex_III <- grep("^UQC", rownames(heatmap_matrix), value = TRUE)
  complex_IV <- grep("^COX", rownames(heatmap_matrix), value = TRUE)
  complex_V <- grep("^ATP", rownames(heatmap_matrix), value = TRUE)
  other_genes <- setdiff(rownames(heatmap_matrix), c(complex_I, complex_II, complex_III, complex_IV, complex_V))

  # 创建行注释
  row_annotation <- data.frame(
    Complex = character(nrow(heatmap_matrix)),
    stringsAsFactors = FALSE
  )
  rownames(row_annotation) <- rownames(heatmap_matrix)

  row_annotation[complex_I, "Complex"] <- "Complex I (NADH)"
  row_annotation[complex_II, "Complex"] <- "Complex II (Succinate)"
  row_annotation[complex_III, "Complex"] <- "Complex III (Cytochrome bc1)"
  row_annotation[complex_IV, "Complex"] <- "Complex IV (Cytochrome c oxidase)"
  row_annotation[complex_V, "Complex"] <- "Complex V (ATP synthase)"
  row_annotation[other_genes, "Complex"] <- "Other"

  # 设置行注释颜色
  row_colors <- list(
    Complex = c(
      "Complex I (NADH)" = "#FF6B6B",
      "Complex II (Succinate)" = "#4ECDC4",
      "Complex III (Cytochrome bc1)" = "#45B7D1",
      "Complex IV (Cytochrome c oxidase)" = "#96CEB4",
      "Complex V (ATP synthase)" = "#FFEAA7",
      "Other" = "#DDA0DD"
    )
  )

  # 绘制功能分组热图
  png("TRMT10C_energy_metabolism_functional_heatmap.png", width = 12, height = 14, units = "in", res = 300)

  pheatmap(
    heatmap_matrix,
    color = colorRampPalette(c("#2166AC", "white", "#B2182B"))(100),
    breaks = seq(-1, 1, length.out = 101),
    cluster_rows = TRUE,
    cluster_cols = FALSE,
    annotation_col = col_annotation,
    annotation_row = row_annotation,
    annotation_colors = c(annotation_colors, row_colors),
    show_rownames = TRUE,
    show_colnames = TRUE,
    fontsize = 10,
    fontsize_row = 8,
    fontsize_col = 10,
    angle_col = 45,
    na_col = "grey90",
    main = "TRMT10C与能量代谢相关基因的功能分组热图\n(按呼吸链复合体分类)",
    cellwidth = 80,
    cellheight = 12
  )

  dev.off()

  return(row_annotation)
}

row_annotation <- create_functional_heatmap()

cat("热图已保存为: TRMT10C_energy_metabolism_heatmap.png\n")
cat("功能分组热图已保存为: TRMT10C_energy_metabolism_functional_heatmap.png\n")

# 5. 保存处理后的数据
write.csv(correlation_matrix, "TRMT10C_energy_metabolism_correlation_matrix.csv", row.names = TRUE)
cat("相关性矩阵已保存为: TRMT10C_energy_metabolism_correlation_matrix.csv\n")

# 6. 生成详细的数据摘要表格
cat("\n=== 生成数据摘要表格 ===\n")

# 创建基因功能分类摘要
create_gene_summary <- function() {
  gene_summary <- data.frame(
    Gene = rownames(heatmap_matrix),
    CPTAC_mRNA_vs_Protein = heatmap_matrix[, "CPTAC_mRNA_vs_Protein"],
    CPTAC_Protein_vs_Protein = heatmap_matrix[, "CPTAC_Protein_vs_Protein"],
    Complex = row_annotation$Complex,
    stringsAsFactors = FALSE
  )

  # 添加显著性标记
  gene_summary$CPTAC_mRNA_Significance <- ifelse(abs(gene_summary$CPTAC_mRNA_vs_Protein) > 0.3, "Strong",
                                                ifelse(abs(gene_summary$CPTAC_mRNA_vs_Protein) > 0.1, "Moderate", "Weak"))
  gene_summary$CPTAC_Protein_Significance <- ifelse(abs(gene_summary$CPTAC_Protein_vs_Protein) > 0.3, "Strong",
                                                   ifelse(abs(gene_summary$CPTAC_Protein_vs_Protein) > 0.1, "Moderate", "Weak"))

  # 处理NA值
  gene_summary$CPTAC_mRNA_Significance[is.na(gene_summary$CPTAC_mRNA_vs_Protein)] <- "No Data"
  gene_summary$CPTAC_Protein_Significance[is.na(gene_summary$CPTAC_Protein_vs_Protein)] <- "No Data"

  return(gene_summary)
}

gene_summary <- create_gene_summary()

# 保存基因摘要表格
write.csv(gene_summary, "TRMT10C_energy_metabolism_gene_summary.csv", row.names = FALSE)
cat("基因摘要表格已保存为: TRMT10C_energy_metabolism_gene_summary.csv\n")

# 创建复合体水平的摘要
create_complex_summary <- function() {
  complex_summary <- gene_summary %>%
    group_by(Complex) %>%
    summarise(
      Gene_Count = n(),
      CPTAC_mRNA_Mean = round(mean(CPTAC_mRNA_vs_Protein, na.rm = TRUE), 3),
      CPTAC_mRNA_SD = round(sd(CPTAC_mRNA_vs_Protein, na.rm = TRUE), 3),
      CPTAC_Protein_Mean = round(mean(CPTAC_Protein_vs_Protein, na.rm = TRUE), 3),
      CPTAC_Protein_SD = round(sd(CPTAC_Protein_vs_Protein, na.rm = TRUE), 3),
      Strong_Correlations = sum(abs(CPTAC_mRNA_vs_Protein) > 0.3 | abs(CPTAC_Protein_vs_Protein) > 0.3, na.rm = TRUE),
      .groups = 'drop'
    )

  return(complex_summary)
}

complex_summary <- create_complex_summary()
write.csv(complex_summary, "TRMT10C_energy_metabolism_complex_summary.csv", row.names = FALSE)
cat("复合体摘要表格已保存为: TRMT10C_energy_metabolism_complex_summary.csv\n")

# 7. 生成最终分析报告
cat("\n=== 最终分析报告 ===\n")
cat("数据来源说明:\n")
cat("1. CPTAC (Cell 2021, 80样本):\n")
cat("   - mRNA FPKM vs TRMT10C蛋白丰度: 75个能量代谢基因\n")
cat("   - 蛋白丰度 vs TRMT10C蛋白丰度: 62个能量代谢基因\n")
cat("2. TCGA数据集中未发现能量代谢相关基因\n")

cat("\n主要发现:\n")
cat("- 总共分析了", nrow(heatmap_matrix), "个能量代谢相关基因\n")
cat("- CPTAC mRNA数据平均相关性:", round(mean(heatmap_matrix[,1], na.rm = TRUE), 3), "\n")
cat("- CPTAC蛋白数据平均相关性:", round(mean(heatmap_matrix[,2], na.rm = TRUE), 3), "\n")

# 找出最强相关性的基因
top_positive_mrna <- rownames(heatmap_matrix)[which.max(heatmap_matrix[,1])]
top_negative_mrna <- rownames(heatmap_matrix)[which.min(heatmap_matrix[,1])]
top_positive_protein <- rownames(heatmap_matrix)[which.max(heatmap_matrix[,2])]
top_negative_protein <- rownames(heatmap_matrix)[which.min(heatmap_matrix[,2])]

cat("\n最强相关性基因:\n")
cat("- mRNA水平正相关最强:", top_positive_mrna, "(r =", round(max(heatmap_matrix[,1], na.rm = TRUE), 3), ")\n")
cat("- mRNA水平负相关最强:", top_negative_mrna, "(r =", round(min(heatmap_matrix[,1], na.rm = TRUE), 3), ")\n")
cat("- 蛋白水平正相关最强:", top_positive_protein, "(r =", round(max(heatmap_matrix[,2], na.rm = TRUE), 3), ")\n")
cat("- 蛋白水平负相关最强:", top_negative_protein, "(r =", round(min(heatmap_matrix[,2], na.rm = TRUE), 3), ")\n")

cat("\n输出文件:\n")
cat("1. TRMT10C_energy_metabolism_heatmap.png - 主要热图\n")
cat("2. TRMT10C_energy_metabolism_functional_heatmap.png - 功能分组热图\n")
cat("3. TRMT10C_energy_metabolism_correlation_matrix.csv - 相关性矩阵\n")
cat("4. TRMT10C_energy_metabolism_gene_summary.csv - 基因水平摘要\n")
cat("5. TRMT10C_energy_metabolism_complex_summary.csv - 复合体水平摘要\n")

cat("\n分析完成!\n")
