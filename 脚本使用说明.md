# TRMT10C能量代谢基因相关性分析脚本使用说明

## 脚本概述

本R脚本(`TRMT10C_energy_metabolism_analysis.R`)用于分析TRMT10C与能量代谢相关基因的相关性。脚本整合了多个数据集的数据，进行全面的相关性分析，并生成详细的结果表格和可视化图表。

## 数据来源说明

### 1. CPTAC Cell 2021数据集 (80样本)
- **文件1**: `mRNAFPKM vs TRMT10C蛋白丰度 (CPTAC, Cell 2021).tsv`
  - 比较对象: mRNA FPKM表达水平 vs TRMT10C蛋白丰度
  - 样本数量: 80个样本
  - 数据类型: 转录组mRNA表达与蛋白质组TRMT10C蛋白丰度的相关性

- **文件2**: `蛋白丰度vs TRMT10C蛋白丰度 (CPTAC, Cell 2021).tsv`
  - 比较对象: 其他蛋白丰度 vs TRMT10C蛋白丰度
  - 样本数量: 80个样本
  - 数据类型: 蛋白质组水平的相关性分析

### 2. TCGA Firehose Legacy数据集 (511样本)
- **文件**: `蛋白表达RPPA vs TRMT10CmRNA (TCGA, Firehose Legacy).tsv`
  - 比较对象: 蛋白表达RPPA vs TRMT10C mRNA表达
  - 样本数量: 511个样本
  - 数据类型: 反向蛋白质阵列(RPPA)蛋白表达与TRMT10C mRNA表达的相关性

### 3. TCGA PanCancer Atlas数据集 (487样本)
- **文件**: `蛋白表达RPPA vs TRMT10CmRNA (TCGA, PanCancer Atlas).tsv`
  - 比较对象: 蛋白表达RPPA vs TRMT10C mRNA表达
  - 样本数量: 487个样本
  - 数据类型: 泛癌症图谱中的蛋白表达与TRMT10C mRNA表达的相关性

## 数据处理流程

### 步骤1: 基因列表准备
- 从`能量代谢相关基因.txt`文件读取90个能量代谢相关基因
- 移除质控基因(ACTB, GAPDH, HPRT1, 18S, NTC)
- 最终分析基因数量: 90个

### 步骤2: 数据读取与整合
- 读取4个TSV格式的相关性分析结果文件
- 每个文件包含: 基因名称、染色体位置、Spearman相关系数、p值、q值

### 步骤3: 能量代谢基因筛选
- 从每个数据集中筛选出能量代谢相关基因的相关性数据
- 添加数据集标识、比较类型、样本数量等元信息

### 步骤4: 数据质量控制
- 计算显著性统计: p < 0.05和q < 0.05的结果数量
- 按数据集统计分析结果
- 计算平均相关系数

### 步骤5: 结果整理与注释
- 添加显著性标记: *** (q<0.001), ** (q<0.01), * (q<0.05), . (p<0.05)
- 添加相关性强度分类: 强相关(≥0.7), 中等相关(≥0.5), 弱相关(≥0.3)
- 添加相关性方向标记: 正相关/负相关

## 输出文件说明

### 1. Excel分析结果文件
**文件名**: `TRMT10C_能量代谢基因相关性分析结果.xlsx`

包含4个工作表:
- **完整结果**: 所有能量代谢基因的相关性分析结果
- **显著结果摘要**: 仅包含p < 0.05的显著结果，按q值排序
- **数据集统计**: 各数据集的统计摘要信息
- **分析说明**: 详细的分析方法和数据来源说明

### 2. 可视化图表
- **相关性分布图**: `TRMT10C_correlation_distribution.png`
  - 显示各数据集中相关系数的分布直方图
  
- **显著性散点图**: `TRMT10C_significance_scatter.png`
  - X轴: Spearman相关系数
  - Y轴: -log10(p值)
  - 颜色区分不同数据集
  - 红色虚线标记p=0.05显著性阈值

- **相关性热图**: `TRMT10C_energy_metabolism_heatmap.png`
  - 仅显示在≥2个数据集中都显著的基因
  - 颜色表示相关系数强度和方向

### 3. 分析报告
**文件名**: `TRMT10C_analysis_report.md`
- Markdown格式的详细分析报告
- 包含数据来源、方法、主要结果和文件说明

## 统计方法说明

### 相关性分析
- **方法**: Spearman秩相关分析
- **原因**: 适用于非正态分布数据，对异常值不敏感

### 显著性检验
- **p值**: 原始显著性检验结果
- **q值**: FDR(False Discovery Rate)多重检验校正后的结果
- **显著性标准**: p < 0.05为显著，q < 0.05为高度显著

### 数据完整性
- 保留所有原始p值和q值的完整精度
- 相关系数保留4位小数
- 完整记录数据来源和比较对象信息

## 主要发现

根据分析结果:
- **总分析记录数**: 137条
- **显著相关记录数** (p < 0.05): 35条
- **高度显著记录数** (q < 0.05): 12条
- **涉及独特基因数**: 75个

### 数据集特异性结果
1. **CPTAC mRNA vs 蛋白**: 75个基因中20个显著，6个高度显著
2. **CPTAC 蛋白 vs 蛋白**: 62个基因中15个显著，6个高度显著
3. **TCGA数据集**: 在能量代谢基因中未发现显著相关性

## 使用建议

1. **结果解读**: 重点关注q < 0.05的高度显著结果
2. **数据集比较**: CPTAC数据显示更强的相关性信号
3. **生物学意义**: 结合基因功能和通路分析进一步解释结果
4. **验证实验**: 建议对显著相关的基因进行实验验证

## 技术要求

### R包依赖
```r
library(readr)      # 数据读取
library(dplyr)      # 数据处理
library(openxlsx)   # Excel文件操作
library(ggplot2)    # 数据可视化
library(pheatmap)   # 热图绘制
library(RColorBrewer) # 颜色配置
library(tibble)     # 数据框操作
library(tidyr)      # 数据整理
```

### 运行环境
- R版本: ≥ 4.0.0
- 操作系统: Windows/Linux/macOS
- 内存要求: ≥ 4GB

## 联系信息

如有问题或需要进一步分析，请联系生物信息学分析团队。
