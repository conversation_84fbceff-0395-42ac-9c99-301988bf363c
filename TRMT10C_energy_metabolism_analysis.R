# TRMT10C相关能量代谢基因分析脚本
# 作者: 生物信息学分析
# 日期: 2025-07-08
# 目的: 分析TRMT10C与能量代谢相关基因的相关性

# 加载必要的包
library(readr)
library(dplyr)
library(openxlsx)
library(ggplot2)
library(pheatmap)
library(RColorBrewer)
library(tibble)
library(tidyr)

# 设置工作目录
setwd(".")

# 1. 读取能量代谢相关基因列表
cat("=== 步骤1: 读取能量代谢相关基因列表 ===\n")
energy_genes_raw <- readLines("能量代谢相关基因.txt")
# 解析基因列表（以逗号分隔）
energy_genes <- trimws(unlist(strsplit(energy_genes_raw, ",")))
# 移除控制基因和空值
energy_genes <- energy_genes[!energy_genes %in% c("ACTB", "GAPDH", "HPRT1", "18S", "NTC", "")]
cat("能量代谢相关基因总数:", length(energy_genes), "\n")
cat("前10个基因:", paste(head(energy_genes, 10), collapse=", "), "\n\n")

# 2. 读取各个数据集
cat("=== 步骤2: 读取TRMT10C相关性数据 ===\n")

# 2.1 CPTAC Cell 2021数据集 - mRNA vs 蛋白
cat("读取CPTAC Cell 2021 mRNA vs 蛋白数据...\n")
cptac_mrna_protein <- read_tsv("CPTAC_Cell 2021_80 Samples/mRNAFPKM vs TRMT10C蛋白丰度 (CPTAC, Cell 2021).tsv")
cat("数据维度:", nrow(cptac_mrna_protein), "行,", ncol(cptac_mrna_protein), "列\n")

# 2.2 CPTAC Cell 2021数据集 - 蛋白 vs 蛋白
cat("读取CPTAC Cell 2021 蛋白 vs 蛋白数据...\n")
cptac_protein_protein <- read_tsv("CPTAC_Cell 2021_80 Samples/蛋白丰度vs TRMT10C蛋白丰度 (CPTAC, Cell 2021).tsv")
cat("数据维度:", nrow(cptac_protein_protein), "行,", ncol(cptac_protein_protein), "列\n")

# 2.3 TCGA Firehose Legacy数据集
cat("读取TCGA Firehose Legacy数据...\n")
tcga_firehose <- read_tsv("TCGA_Firehose Legacy_511 Samples/蛋白表达RPPA vs TRMT10CmRNA (TCGA, Firehose Legacy).tsv")
cat("数据维度:", nrow(tcga_firehose), "行,", ncol(tcga_firehose), "列\n")

# 2.4 TCGA PanCancer Atlas数据集
cat("读取TCGA PanCancer Atlas数据...\n")
tcga_pancancer <- read_tsv("TCGA_PanCancer Atlas 487 Samples/蛋白表达RPPA vs TRMT10CmRNA (TCGA, PanCancer Atlas).tsv")
cat("数据维度:", nrow(tcga_pancancer), "行,", ncol(tcga_pancancer), "列\n\n")

# 3. 筛选能量代谢相关基因
cat("=== 步骤3: 筛选能量代谢相关基因 ===\n")

# 3.1 CPTAC mRNA vs 蛋白数据筛选
cptac_mrna_energy <- cptac_mrna_protein %>%
  filter(`Correlated Gene` %in% energy_genes) %>%
  mutate(
    Dataset = "CPTAC Cell 2021",
    Comparison = "mRNA FPKM vs TRMT10C蛋白丰度",
    Sample_Size = "80 Samples"
  )
cat("CPTAC mRNA vs 蛋白 - 能量代谢基因数量:", nrow(cptac_mrna_energy), "\n")

# 3.2 CPTAC 蛋白 vs 蛋白数据筛选
cptac_protein_energy <- cptac_protein_protein %>%
  filter(`Correlated Gene` %in% energy_genes) %>%
  mutate(
    Dataset = "CPTAC Cell 2021",
    Comparison = "蛋白丰度 vs TRMT10C蛋白丰度",
    Sample_Size = "80 Samples"
  )
cat("CPTAC 蛋白 vs 蛋白 - 能量代谢基因数量:", nrow(cptac_protein_energy), "\n")

# 3.3 TCGA Firehose数据筛选
tcga_firehose_energy <- tcga_firehose %>%
  filter(`Correlated Gene` %in% energy_genes) %>%
  mutate(
    Dataset = "TCGA Firehose Legacy",
    Comparison = "蛋白表达RPPA vs TRMT10C mRNA",
    Sample_Size = "511 Samples"
  )
cat("TCGA Firehose - 能量代谢基因数量:", nrow(tcga_firehose_energy), "\n")

# 3.4 TCGA PanCancer数据筛选
tcga_pancancer_energy <- tcga_pancancer %>%
  filter(`Correlated Gene` %in% energy_genes) %>%
  mutate(
    Dataset = "TCGA PanCancer Atlas",
    Comparison = "蛋白表达RPPA vs TRMT10C mRNA",
    Sample_Size = "487 Samples"
  )
cat("TCGA PanCancer - 能量代谢基因数量:", nrow(tcga_pancancer_energy), "\n\n")

# 4. 合并所有数据集
cat("=== 步骤4: 合并数据集 ===\n")
all_energy_results <- bind_rows(
  cptac_mrna_energy,
  cptac_protein_energy,
  tcga_firehose_energy,
  tcga_pancancer_energy
) %>%
  arrange(`p-Value`)

cat("合并后总记录数:", nrow(all_energy_results), "\n")
cat("涉及的独特基因数:", length(unique(all_energy_results$`Correlated Gene`)), "\n\n")

# 5. 数据质量控制和统计摘要
cat("=== 步骤5: 数据质量控制 ===\n")

# 显著性筛选
significant_results <- all_energy_results %>%
  filter(`p-Value` < 0.05)

highly_significant_results <- all_energy_results %>%
  filter(`q-Value` < 0.05)

cat("p < 0.05的显著结果:", nrow(significant_results), "\n")
cat("q < 0.05的高度显著结果:", nrow(highly_significant_results), "\n")

# 按数据集统计
dataset_summary <- all_energy_results %>%
  group_by(Dataset, Comparison) %>%
  summarise(
    Total_Genes = n(),
    Significant_p005 = sum(`p-Value` < 0.05),
    Significant_q005 = sum(`q-Value` < 0.05),
    Mean_Correlation = mean(`Spearman's Correlation`),
    .groups = 'drop'
  )

print(dataset_summary)

# 6. 创建详细的结果表格
cat("\n=== 步骤6: 创建详细结果表格 ===\n")

# 重新整理列名和格式
final_results <- all_energy_results %>%
  select(
    基因名称 = `Correlated Gene`,
    染色体位置 = Cytoband,
    数据集 = Dataset,
    比较类型 = Comparison,
    样本数量 = Sample_Size,
    Spearman相关系数 = `Spearman's Correlation`,
    P值 = `p-Value`,
    Q值 = `q-Value`
  ) %>%
  mutate(
    Spearman相关系数 = round(Spearman相关系数, 4),
    显著性水平 = case_when(
      Q值 < 0.001 ~ "***",
      Q值 < 0.01 ~ "**",
      Q值 < 0.05 ~ "*",
      P值 < 0.05 ~ ".",
      TRUE ~ ""
    ),
    相关性强度 = case_when(
      abs(Spearman相关系数) >= 0.7 ~ "强相关",
      abs(Spearman相关系数) >= 0.5 ~ "中等相关",
      abs(Spearman相关系数) >= 0.3 ~ "弱相关",
      TRUE ~ "极弱相关"
    ),
    相关性方向 = ifelse(Spearman相关系数 > 0, "正相关", "负相关")
  )

# 7. 保存结果到Excel文件
cat("保存结果到Excel文件...\n")
wb <- createWorkbook()

# 工作表1: 完整结果
addWorksheet(wb, "完整结果")
writeData(wb, "完整结果", final_results)

# 工作表2: 显著结果摘要
addWorksheet(wb, "显著结果摘要")
significant_summary <- final_results %>%
  filter(P值 < 0.05) %>%
  arrange(Q值)
writeData(wb, "显著结果摘要", significant_summary)

# 工作表3: 数据集统计
addWorksheet(wb, "数据集统计")
writeData(wb, "数据集统计", dataset_summary)

# 工作表4: 分析说明
addWorksheet(wb, "分析说明")
analysis_info <- data.frame(
  项目 = c(
    "分析目的",
    "数据来源1",
    "数据来源2", 
    "数据来源3",
    "数据来源4",
    "能量代谢基因数量",
    "分析方法",
    "显著性标准",
    "相关性计算方法"
  ),
  说明 = c(
    "分析TRMT10C与能量代谢相关基因的相关性",
    "CPTAC Cell 2021 (80样本): mRNA FPKM vs TRMT10C蛋白丰度",
    "CPTAC Cell 2021 (80样本): 蛋白丰度 vs TRMT10C蛋白丰度",
    "TCGA Firehose Legacy (511样本): 蛋白表达RPPA vs TRMT10C mRNA",
    "TCGA PanCancer Atlas (487样本): 蛋白表达RPPA vs TRMT10C mRNA",
    paste(length(energy_genes), "个基因"),
    "Spearman相关性分析",
    "p < 0.05为显著，q < 0.05为高度显著（FDR校正）",
    "Spearman秩相关系数"
  )
)
writeData(wb, "分析说明", analysis_info)

saveWorkbook(wb, "TRMT10C_能量代谢基因相关性分析结果.xlsx", overwrite = TRUE)

cat("分析完成！结果已保存到 'TRMT10C_能量代谢基因相关性分析结果.xlsx'\n")
cat("文件包含4个工作表：完整结果、显著结果摘要、数据集统计、分析说明\n\n")

# 8. 创建可视化图表
cat("=== 步骤7: 创建可视化图表 ===\n")

# 8.1 相关性分布直方图
cat("创建相关性分布图...\n")
p1 <- ggplot(final_results, aes(x = Spearman相关系数)) +
  geom_histogram(bins = 30, fill = "steelblue", alpha = 0.7) +
  facet_wrap(~数据集, scales = "free_y") +
  labs(
    title = "TRMT10C与能量代谢基因相关性分布",
    x = "Spearman相关系数",
    y = "基因数量"
  ) +
  theme_minimal() +
  theme(
    plot.title = element_text(hjust = 0.5, size = 14),
    strip.text = element_text(size = 10)
  )

ggsave("TRMT10C_correlation_distribution.png", p1, width = 12, height = 8, dpi = 300)

# 8.2 显著性散点图
cat("创建显著性散点图...\n")
plot_data <- final_results %>%
  mutate(
    log10_p = -log10(P值),
    log10_q = -log10(Q值),
    is_significant = P值 < 0.05
  )

p2 <- ggplot(plot_data, aes(x = Spearman相关系数, y = log10_p)) +
  geom_point(aes(color = 数据集, size = is_significant), alpha = 0.7) +
  scale_size_manual(values = c(1, 2), guide = "none") +
  geom_hline(yintercept = -log10(0.05), linetype = "dashed", color = "red") +
  labs(
    title = "TRMT10C与能量代谢基因相关性显著性分析",
    x = "Spearman相关系数",
    y = "-log10(P值)",
    color = "数据集"
  ) +
  theme_minimal() +
  theme(
    plot.title = element_text(hjust = 0.5, size = 14),
    legend.position = "bottom"
  )

ggsave("TRMT10C_significance_scatter.png", p2, width = 12, height = 8, dpi = 300)

# 8.3 热图数据准备
cat("准备热图数据...\n")
# 选择在至少2个数据集中都显著的基因
gene_significance <- final_results %>%
  filter(P值 < 0.05) %>%
  group_by(基因名称) %>%
  summarise(
    dataset_count = n(),
    mean_correlation = mean(Spearman相关系数),
    .groups = 'drop'
  ) %>%
  filter(dataset_count >= 2) %>%
  arrange(desc(abs(mean_correlation)))

if(nrow(gene_significance) > 0) {
  # 创建热图矩阵
  heatmap_data <- final_results %>%
    filter(基因名称 %in% gene_significance$基因名称) %>%
    select(基因名称, 比较类型, Spearman相关系数) %>%
    pivot_wider(
      names_from = 比较类型,
      values_from = Spearman相关系数,
      values_fill = NA
    ) %>%
    column_to_rownames("基因名称")

  # 创建热图
  cat("创建相关性热图...\n")
  png("TRMT10C_energy_metabolism_heatmap.png", width = 12, height = 8, units = "in", res = 300)
  pheatmap(
    heatmap_data,
    color = colorRampPalette(c("blue", "white", "red"))(100),
    breaks = seq(-1, 1, length.out = 101),
    cluster_rows = TRUE,
    cluster_cols = FALSE,
    display_numbers = TRUE,
    number_format = "%.3f",
    fontsize = 10,
    fontsize_number = 8,
    main = "TRMT10C与能量代谢基因相关性热图\n(仅显示在≥2个数据集中显著的基因)",
    na_col = "grey90"
  )
  dev.off()
} else {
  cat("没有在多个数据集中都显著的基因，跳过热图创建\n")
}

# 9. 生成分析报告
cat("\n=== 步骤8: 生成分析报告 ===\n")
report_lines <- c(
  "# TRMT10C与能量代谢相关基因相关性分析报告",
  "",
  paste("## 分析日期:", Sys.Date()),
  "",
  "## 数据来源",
  "1. **CPTAC Cell 2021 (80样本)**",
  "   - mRNA FPKM vs TRMT10C蛋白丰度",
  "   - 蛋白丰度 vs TRMT10C蛋白丰度",
  "",
  "2. **TCGA Firehose Legacy (511样本)**",
  "   - 蛋白表达RPPA vs TRMT10C mRNA",
  "",
  "3. **TCGA PanCancer Atlas (487样本)**",
  "   - 蛋白表达RPPA vs TRMT10C mRNA",
  "",
  "## 分析方法",
  "- 相关性分析方法: Spearman秩相关",
  "- 显著性标准: p < 0.05",
  "- 多重检验校正: FDR (q值)",
  paste("- 能量代谢基因总数:", length(energy_genes)),
  "",
  "## 主要结果",
  paste("- 总分析记录数:", nrow(all_energy_results)),
  paste("- 显著相关记录数 (p < 0.05):", nrow(significant_results)),
  paste("- 高度显著记录数 (q < 0.05):", nrow(highly_significant_results)),
  paste("- 涉及独特基因数:", length(unique(all_energy_results$`Correlated Gene`))),
  "",
  "## 数据集特异性结果"
)

# 添加每个数据集的统计
for(i in 1:nrow(dataset_summary)) {
  report_lines <- c(report_lines,
    paste("### ", dataset_summary$Dataset[i], " - ", dataset_summary$Comparison[i]),
    paste("- 总基因数:", dataset_summary$Total_Genes[i]),
    paste("- 显著基因数 (p < 0.05):", dataset_summary$Significant_p005[i]),
    paste("- 高度显著基因数 (q < 0.05):", dataset_summary$Significant_q005[i]),
    paste("- 平均相关系数:", round(dataset_summary$Mean_Correlation[i], 4)),
    ""
  )
}

report_lines <- c(report_lines,
  "## 输出文件",
  "1. `TRMT10C_能量代谢基因相关性分析结果.xlsx` - 详细分析结果",
  "2. `TRMT10C_correlation_distribution.png` - 相关性分布图",
  "3. `TRMT10C_significance_scatter.png` - 显著性散点图",
  "4. `TRMT10C_energy_metabolism_heatmap.png` - 相关性热图",
  "5. `TRMT10C_analysis_report.md` - 本分析报告",
  "",
  "## 注意事项",
  "- 所有p值和q值均保留原始精度",
  "- 相关性系数保留4位小数",
  "- 显著性标记: *** (q<0.001), ** (q<0.01), * (q<0.05), . (p<0.05)",
  "",
  paste("分析完成时间:", Sys.time())
)

writeLines(report_lines, "TRMT10C_analysis_report.md")

cat("所有分析完成！\n")
cat("输出文件:\n")
cat("1. TRMT10C_能量代谢基因相关性分析结果.xlsx\n")
cat("2. TRMT10C_correlation_distribution.png\n")
cat("3. TRMT10C_significance_scatter.png\n")
cat("4. TRMT10C_energy_metabolism_heatmap.png\n")
cat("5. TRMT10C_analysis_report.md\n")
